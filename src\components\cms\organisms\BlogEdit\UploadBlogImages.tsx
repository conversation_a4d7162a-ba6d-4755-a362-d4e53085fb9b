import React, { useState, useCallback, useEffect } from "react";
import {
    Button,
    Upload,
    Modal,
    Tabs,
    Form,
    Input,
    Divider,
    message,
    Image,
    Badge,
    Tooltip,
    Alert,
} from "antd";
import type { UploadFile, UploadProps } from "antd";
import {
    ImageIcon,
    ImagePlusIcon,
    InboxIcon,
    XIcon,
    Link2Icon,
    UnlinkIcon,
} from "lucide-react";
import {
    uploadFile,
    updateFile,
    FileUploadOptions,
} from "@services/portals/shared/file";
import { File } from "@myTypes/file";
import { RcFile } from "antd/es/upload";
import { DeleteConfirm } from "@components/shared/atoms/DeleteConfirm";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { deleteImage } from "@services/portals/cms/blogs/post";
import { getImageDimensions } from "@lib/images";
import { BlogCardPreview } from "@components/cms/organisms/BlogPreview/blogCards";
import { BlogPost } from "@myTypes/blog";

const { Dragger } = Upload;

const BLOG_COVER_DIMENSIONS = { width: 1600, height: 840 };
const BLOG_THUMBNAIL_DIMENSIONS = { width: 600, height: 400 };

// Tipo para el estado de la imagen
interface ImageState {
    file: UploadFile | null;
    id: string | null;
    description: string;
    width?: number;
    height?: number;
    isModalVisible: boolean;
    type: "cover" | "thumbnail";
    aspectRatio?: number;
    maintainAspectRatio: boolean;
}

interface UploadBlogImagesProps {
    blog: BlogPost;
    coverImage?: File | null;
    thumbnail?: File | null;
    description?: string;
    onImageUpdate: (type: "cover" | "thumbnail", imageId: string) => void;
}

type DetectChangesArgs<T, Q> = {
    data: T | undefined;
    values: Q;
    fieldsToCompare: (keyof T)[];
};

const detectChanges = ({
    data,
    values,
    fieldsToCompare,
}: DetectChangesArgs<Partial<ImageState>, Partial<ImageState>>): boolean => {
    if (data === undefined) {
        throw new Error("No data provided");
    }
    for (const field of fieldsToCompare) {
        if (data[field] !== values[field]) {
            return true;
        }
    }

    return false;
};

const UploadBlogImages: React.FC<UploadBlogImagesProps> = ({
    blog,
    description,
    coverImage,
    thumbnail,
    onImageUpdate,
}) => {
    const [messageApi, contextHolder] = message.useMessage();
    const queryClient = useQueryClient();
    const [imageForm] = Form.useForm<ImageState>();
    const blogId = blog.bid;

    // Estado unificado para las imágenes
    const [imageState, setImageState] = useState<ImageState>({
        file: null,
        id: null,
        description: "",
        width: BLOG_COVER_DIMENSIONS.width,
        height: BLOG_COVER_DIMENSIONS.height,
        isModalVisible: false,
        type: "cover",
        aspectRatio: BLOG_COVER_DIMENSIONS.width / BLOG_COVER_DIMENSIONS.height,
        maintainAspectRatio: true,
    });

    // Estado para la vista previa de la imagen
    const [previewOpen, setPreviewOpen] = useState(false);
    const [previewImage, setPreviewImage] = useState("");

    /**
     * Verifica si las dimensiones están dentro del rango recomendado
     */
    const isWithinRecommendedDimensions = useCallback(
        (width: number, height: number, type: "cover" | "thumbnail") => {
            const recommended =
                type === "cover" ? BLOG_COVER_DIMENSIONS : BLOG_THUMBNAIL_DIMENSIONS;
            const tolerance = 0.1; // 10% de tolerancia

            const widthDiff = Math.abs(width - recommended.width) / recommended.width;
            const heightDiff =
                Math.abs(height - recommended.height) / recommended.height;

            return widthDiff <= tolerance && heightDiff <= tolerance;
        },
        [],
    );

    /**
     * Alterna el estado de mantener relación de aspecto
     */
    const toggleAspectRatio = useCallback(() => {
        setImageState((prev) => ({
            ...prev,
            maintainAspectRatio: !prev.maintainAspectRatio,
        }));
    }, []);

    // Efecto para sincronizar el formulario cuando cambian las dimensiones programáticamente
    useEffect(() => {
        if (imageState.width && imageState.height) {
            imageForm.setFieldsValue({
                width: imageState.width,
                height: imageState.height,
            });
        }
    }, [imageState.width, imageState.height, imageForm]);

    /**
     * Abre el modal para cargar una imagen
     */
    const openImageModal = useCallback(
        (type: "cover" | "thumbnail") => {
            // Inicializar el formulario con los valores actuales
            const currentFile = type === "cover" ? coverImage : thumbnail;

            // Determinar las dimensiones predeterminadas según el tipo de imagen
            const defaultDimensions =
                type === "cover" ? BLOG_COVER_DIMENSIONS : BLOG_THUMBNAIL_DIMENSIONS;

            imageForm.setFieldsValue({
                description: currentFile?.description || description || "",
                width: currentFile?.width || defaultDimensions.width,
                height: currentFile?.height || defaultDimensions.height,
            });

            const currentWidth = currentFile?.width || defaultDimensions.width;
            const currentHeight = currentFile?.height || defaultDimensions.height;
            const aspectRatio = currentWidth / currentHeight;

            setImageState((prev) => ({
                ...prev,
                type,
                isModalVisible: true,
                file:
                    type === "cover"
                        ? coverImage
                            ? {
                                  uid: coverImage.fid,
                                  name: coverImage.name,
                                  status: "done",
                                  url: coverImage.url,
                              }
                            : null
                        : thumbnail
                          ? {
                                uid: thumbnail.fid,
                                name: thumbnail.name,
                                status: "done",
                                url: thumbnail.url,
                            }
                          : null,
                description: currentFile?.description || description || "",
                width: currentWidth,
                height: currentHeight,
                aspectRatio: aspectRatio,
                maintainAspectRatio: true,
            }));
        },
        [imageForm, coverImage, thumbnail, description],
    );

    /**
     * Cierra el modal de carga de imágenes
     */
    const closeImageModal = useCallback(() => {
        setImageState((prev) => ({
            ...prev,
            isModalVisible: false,
        }));
    }, []);

    /**
     * Maneja la carga de la imagen
     */
    const handleImageUpload = useCallback(
        (options: {
            file: string | Blob | RcFile;
            onSuccess: (response: File) => void;
            onError: (error: Error) => void;
            description?: string;
        }) => {
            const { file, onSuccess, onError, description } = options;

            (async () => {
                try {
                    // Asegurarse de que siempre haya una descripción
                    const safeDescription = description || "";
                    // Obtener dimensiones originales de la imagen
                    const { width, height } = await getImageDimensions(file as RcFile);
                    const aspectRatio = width / height;

                    setImageState((prev) => ({
                        ...prev,
                        width,
                        height,
                        aspectRatio,
                    }));
                    // Opciones para la carga del archivo
                    const uploadOptions: FileUploadOptions = {
                        description: safeDescription,
                        width: width,
                        height: height,
                        outputFormat: "WEBP", // Formato más eficiente para web
                    };

                    // Subir el archivo al servidor
                    const uploadedFile = await uploadFile(file as Blob, uploadOptions);

                    // Actualizar el estado con el archivo cargado
                    setImageState((prev) => ({
                        ...prev,
                        file: {
                            uid: uploadedFile.fid,
                            name: uploadedFile.name,
                            status: "done",
                            url: uploadedFile.url,
                        } as UploadFile,
                        id: uploadedFile.fid,
                        description: uploadedFile.description || "",
                        width: uploadedFile.width || prev.width,
                        height: uploadedFile.height || prev.height,
                    }));

                    // Actualizar el formulario con las dimensiones reales de la imagen
                    imageForm.setFieldsValue({
                        description: uploadedFile.description || "",
                        width: uploadedFile.width || imageState.width,
                        height: uploadedFile.height || imageState.height,
                    });

                    // Notificar éxito
                    onSuccess(uploadedFile);
                } catch (error) {
                    onError(error as Error);
                    messageApi.error("Error al cargar la imagen");
                }
            })();
        },
        [messageApi, imageState.width, imageState.height, imageForm],
    );

    /**
     * Guarda los cambios de la imagen y cierra el modal
     */
    const saveImageChanges = useCallback(async () => {
        try {
            // Validar el formulario
            const values = await imageForm.validateFields();

            // Actualizar el estado con la descripción y dimensiones
            setImageState((prev) => ({
                ...prev,
                id:
                description: values.description,
                width: values.width,
                height: values.height,
            }));

            // Si hay un archivo cargado, guardar los cambios
            const currentFile = imageState.type === "cover" ? coverImage : thumbnail;

            if (imageState.file && imageState.id) {
                // Actualizar la descripción y dimensiones del archivo
                await updateFile(imageState.id, undefined, {
                    description: values.description,
                    width: values.width,
                    height: values.height,
                });

                // Actualizar las propiedades en el componente padre
                if (imageState.type === "cover" && coverImage) {
                    coverImage.description = values.description;
                    coverImage.width = values.width;
                    coverImage.height = values.height;
                } else if (imageState.type === "thumbnail" && thumbnail) {
                    thumbnail.description = values.description;
                    thumbnail.width = values.width;
                    thumbnail.height = values.height;
                }

                // Notificar al componente padre sobre la actualización
                onImageUpdate(imageState.type, imageState.id);

                // Cerrar el modal
                // closeImageModal();

                // Notificar éxito
                messageApi.success(
                    `Imagen de ${
                        imageState.type === "cover" ? "portada" : "miniatura"
                    } guardada correctamente`,
                );
            } else if (
                detectChanges({
                    data: {
                        description: currentFile?.description || "",
                        width: currentFile?.width || 0,
                        height: currentFile?.height || 0,
                    },
                    values: values,
                    fieldsToCompare: ["description", "width", "height"],
                })
            ) {
                // Si solo cambió la descripción y ya existe un archivo
                const currentFile =
                    imageState.type === "cover" ? coverImage : thumbnail;

                if (currentFile) {
                    // Actualizar la descripción y dimensiones del archivo existente
                    await updateFile(currentFile.fid, undefined, {
                        description: values.description,
                        width: values.width,
                        height: values.height,
                    });

                    // Actualizar las propiedades en el componente padre
                    currentFile.description = values.description;
                    currentFile.width = values.width;
                    currentFile.height = values.height;

                    // Notificar al componente padre sobre la actualización
                    if (onImageUpdate) {
                        onImageUpdate(imageState.type, currentFile.fid);
                    }

                    // closeImageModal();

                    message.success(
                        `${imageState.type === "cover" ? "Portada" : "Miniatura"} actualizada correctamente`,
                    );
                }
            } else {
                // Si no hay cambios, solo cerrar el modal
                closeImageModal();
            }
        } catch (error) {
            message.error("Error al guardar la imagen");
        }
    }, [
        imageState,
        imageForm,
        closeImageModal,
        messageApi,
        coverImage,
        thumbnail,
        onImageUpdate,
    ]);

    // Configuración para el componente Dragger
    const draggerProps: UploadProps = {
        name: "file",
        multiple: false,
        customRequest: (options) => {
            // Primero validar el formulario antes de subir la imagen
            imageForm
                .validateFields()
                .then((values) => {
                    const { onError, onSuccess, file } = options;

                    // Asegurarse de que la descripción existe
                    if (!values.description) {
                        messageApi.error(
                            "Por favor ingrese una descripción para la imagen",
                        );
                        if (onError) onError(new Error("Descripción requerida"));
                        return;
                    }

                    handleImageUpload({
                        file: file as Blob,
                        description: values.description,
                        onSuccess: (response) => onSuccess && onSuccess(response),
                        onError: (error) => onError && onError(error),
                    });
                })
                .catch((error) => {
                    console.error("Error de validación:", error);
                    messageApi.error(
                        "Por favor complete todos los campos requeridos antes de subir la imagen",
                    );
                });
        },
        showUploadList: false,
        accept: "image/*",
        onChange(info) {
            if (info.file.status === "done") {
                messageApi.success("Imagen cargada correctamente");
            } else if (info.file.status === "error") {
                messageApi.error(`Error al cargar ${info.file.name}`);
            }
        },
    };

    /* Partial mutations to delete images */
    const { mutateAsync: deleteImg } = useMutation({
        mutationFn: (type: "cover" | "thumbnail") => deleteImage({ bid: blogId, type }),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["blog", blogId] });
            messageApi.success("Imagen eliminada correctamente");
        },
        onError: () => {
            messageApi.error("Error al eliminar la imagen");
        },
    });

    const DeleteImageButton: React.FC<{
        type: "cover" | "thumbnail";
        deleteImg: (type: "cover" | "thumbnail") => Promise<void>;
        title: string;
        description: string;
    }> = ({ type, deleteImg, title, description }) => {
        return (
            <DeleteConfirm
                title={title}
                description={description}
                onConfirm={() => deleteImg(type)}
                className="absolute -top-1 -right-1"
                customTrigger={
                    <div className="flex items-center gap-2 bg-state-red-full text-white-full rounded-full hover:bg-white-full hover:text-state-red-full cursor-pointer">
                        <XIcon className="w-5 h-5" />
                        <span className="sr-only">Eliminar</span>
                    </div>
                }
            />
        );
    };

    return (
        <>
            {contextHolder}
            <div className="flex items-center gap-4 mb-4">
                {/* Botón para cargar imagen de portada */}
                <Button
                    type="text"
                    className="flex items-center gap-2 hover:bg-gray-100 rounded-md px-3 py-1"
                    icon={<ImageIcon size={18} />}
                    onClick={() => openImageModal("cover")}
                >
                    {coverImage ? "Cambiar portada" : "Añadir portada"}
                </Button>

                {/* Botón para cargar imagen de miniatura */}
                <Button
                    type="text"
                    className="flex items-center gap-2 hover:bg-gray-100 rounded-md px-3 py-1"
                    icon={<ImagePlusIcon size={18} />}
                    onClick={() => openImageModal("thumbnail")}
                >
                    {thumbnail ? "Cambiar miniatura" : "Añadir miniatura"}
                </Button>
            </div>

            {/* Previsualización de imágenes */}
            {(coverImage || thumbnail) && (
                <div className="flex gap-4 mb-4">
                    {coverImage && (
                        <div className="relative">
                            <img
                                src={coverImage.url}
                                alt={coverImage.description || "Portada"}
                                className="w-32 h-20 object-cover rounded-md"
                            />
                            <Badge className="shadow-md absolute bottom-0 -right-2 px-1.5 py-1 bg-blue-full text-white-full rounded-full h-fit">
                                Portada
                            </Badge>
                            {/* Delete cover image */}
                            <DeleteImageButton
                                type="cover"
                                deleteImg={deleteImg}
                                title="Eliminar portada"
                                description="¿Estás seguro de eliminar la portada?"
                            />
                        </div>
                    )}

                    {thumbnail && (
                        <div className="relative">
                            <img
                                src={thumbnail.url}
                                alt={thumbnail.description || "Miniatura"}
                                className="w-32 h-20 object-cover rounded-md"
                            />
                            <Badge className="shadow-md absolute bottom-0 -right-2 px-1.5 py-1 bg-white-full rounded-full h-fit">
                                Miniatura
                            </Badge>
                        </div>
                    )}
                </div>
            )}

            {/* Modal para cargar imágenes */}
            <Modal
                title={`${
                    imageState.type === "cover"
                        ? "Imagen de portada"
                        : "Imagen de miniatura"
                }`}
                open={imageState.isModalVisible}
                onCancel={closeImageModal}
                footer={[
                    <Button key="cancel" onClick={closeImageModal}>
                        Cancelar
                    </Button>,
                    <Button key="save" type="primary" onClick={saveImageChanges}>
                        Guardar
                    </Button>,
                ]}
                width={1000}
                destroyOnClose
                styles={{ body: { maxHeight: "80vh", overflow: "auto" } }}
            >
                <Tabs
                    defaultActiveKey="upload"
                    items={[
                        {
                            key: "upload",
                            label: "Subir",
                            children: (
                                <div>
                                    <div className="flex flex-col md:flex-row gap-2">
                                        <Form
                                            form={imageForm}
                                            layout="vertical"
                                            initialValues={{
                                                description: imageState.description,
                                                width: imageState.width,
                                                height: imageState.height,
                                            }}
                                            className="col-span-5"
                                        >
                                            <Form.Item
                                                name="description"
                                                label="Texto alternativo (descripción)"
                                                rules={[
                                                    {
                                                        required: true,
                                                        message:
                                                            "Por favor ingrese una descripción",
                                                    },
                                                ]}
                                            >
                                                <Input.TextArea
                                                    placeholder="Describa la imagen para mejorar la accesibilidad"
                                                    rows={2}
                                                />
                                            </Form.Item>

                                            <div className="space-y-4">
                                                <div className="flex items-center justify-between">
                                                    <span className="text-sm font-medium">
                                                        Dimensiones
                                                    </span>
                                                    <Tooltip
                                                        title={
                                                            imageState.maintainAspectRatio
                                                                ? "Desactivar relación de aspecto"
                                                                : "Mantener relación de aspecto"
                                                        }
                                                    >
                                                        <Button
                                                            type={
                                                                imageState.maintainAspectRatio
                                                                    ? "primary"
                                                                    : "default"
                                                            }
                                                            size="small"
                                                            icon={
                                                                imageState.maintainAspectRatio ? (
                                                                    <Link2Icon
                                                                        size={14}
                                                                    />
                                                                ) : (
                                                                    <UnlinkIcon
                                                                        size={14}
                                                                    />
                                                                )
                                                            }
                                                            onClick={toggleAspectRatio}
                                                            className="flex items-center gap-1"
                                                        >
                                                            {imageState.maintainAspectRatio
                                                                ? "Vinculado"
                                                                : "Libre"}
                                                        </Button>
                                                    </Tooltip>
                                                </div>

                                                <div className="grid grid-cols-2 gap-4">
                                                    <Form.Item
                                                        name="width"
                                                        label="Ancho (px)"
                                                        rules={[
                                                            {
                                                                required: true,
                                                                message:
                                                                    "Por favor ingrese el ancho",
                                                            },
                                                        ]}
                                                    >
                                                        <Input
                                                            type="number"
                                                            min={100}
                                                            onChange={(e) => {
                                                                const value = parseInt(
                                                                    e.target.value,
                                                                );
                                                                if (!isNaN(value)) {
                                                                    // Actualizar el estado del imageState
                                                                    setImageState(
                                                                        (prev) => ({
                                                                            ...prev,
                                                                            width: value,
                                                                        }),
                                                                    );

                                                                    // Si mantener relación de aspecto está activado, calcular height
                                                                    if (
                                                                        imageState.maintainAspectRatio &&
                                                                        imageState.aspectRatio
                                                                    ) {
                                                                        const newHeight =
                                                                            Math.round(
                                                                                value /
                                                                                    imageState.aspectRatio,
                                                                            );
                                                                        setImageState(
                                                                            (prev) => ({
                                                                                ...prev,
                                                                                height: newHeight,
                                                                            }),
                                                                        );
                                                                        imageForm.setFieldsValue(
                                                                            {
                                                                                height: newHeight,
                                                                            },
                                                                        );
                                                                    }
                                                                }
                                                            }}
                                                        />
                                                    </Form.Item>

                                                    <Form.Item
                                                        name="height"
                                                        label="Alto (px)"
                                                        rules={[
                                                            {
                                                                required: true,
                                                                message:
                                                                    "Por favor ingrese el alto",
                                                            },
                                                        ]}
                                                    >
                                                        <Input
                                                            type="number"
                                                            min={100}
                                                            onChange={(e) => {
                                                                const value = parseInt(
                                                                    e.target.value,
                                                                );
                                                                if (!isNaN(value)) {
                                                                    // Actualizar el estado del imageState
                                                                    setImageState(
                                                                        (prev) => ({
                                                                            ...prev,
                                                                            height: value,
                                                                        }),
                                                                    );

                                                                    // Si mantener relación de aspecto está activado, calcular width
                                                                    if (
                                                                        imageState.maintainAspectRatio &&
                                                                        imageState.aspectRatio
                                                                    ) {
                                                                        const newWidth =
                                                                            Math.round(
                                                                                value *
                                                                                    imageState.aspectRatio,
                                                                            );
                                                                        setImageState(
                                                                            (prev) => ({
                                                                                ...prev,
                                                                                width: newWidth,
                                                                            }),
                                                                        );
                                                                        imageForm.setFieldsValue(
                                                                            {
                                                                                width: newWidth,
                                                                            },
                                                                        );
                                                                    }
                                                                }
                                                            }}
                                                        />
                                                    </Form.Item>
                                                </div>

                                                {/* Indicador de dimensiones recomendadas */}
                                                {imageState.width &&
                                                    imageState.height && (
                                                        <Alert
                                                            message={
                                                                isWithinRecommendedDimensions(
                                                                    imageState.width,
                                                                    imageState.height,
                                                                    imageState.type,
                                                                )
                                                                    ? "✓ Dimensiones dentro del rango recomendado"
                                                                    : `⚠ Dimensiones recomendadas: ${
                                                                          imageState.type ===
                                                                          "cover"
                                                                              ? `${BLOG_COVER_DIMENSIONS.width} × ${BLOG_COVER_DIMENSIONS.height}`
                                                                              : `${BLOG_THUMBNAIL_DIMENSIONS.width} × ${BLOG_THUMBNAIL_DIMENSIONS.height}`
                                                                      } píxeles`
                                                            }
                                                            type={
                                                                isWithinRecommendedDimensions(
                                                                    imageState.width,
                                                                    imageState.height,
                                                                    imageState.type,
                                                                )
                                                                    ? "success"
                                                                    : "warning"
                                                            }
                                                            showIcon
                                                            className="text-xs"
                                                        />
                                                    )}
                                            </div>

                                            <Dragger
                                                {...draggerProps}
                                                className="mt-4 p-0 flex flex-col h-fit"
                                            >
                                                <div className="flex flex-col items-center justify-center">
                                                    <p className="mb-2">
                                                        <InboxIcon size={32} />
                                                    </p>
                                                    <p className="ant-upload-text text-sm mb-1">
                                                        Haga clic o arrastre un archivo
                                                        a esta área
                                                    </p>
                                                    <p className="ant-upload-hint text-xs">
                                                        Dimensión recomendada:{" "}
                                                        {imageState.type === "cover"
                                                            ? `${BLOG_COVER_DIMENSIONS.width} × ${BLOG_COVER_DIMENSIONS.height}`
                                                            : `${BLOG_THUMBNAIL_DIMENSIONS.width} × ${BLOG_THUMBNAIL_DIMENSIONS.height}`}{" "}
                                                        píxeles
                                                    </p>
                                                </div>
                                            </Dragger>
                                        </Form>

                                        <div className="grow">
                                            {imageState.file ? (
                                                <div className="mt-4">
                                                    <Divider>Vista previa</Divider>
                                                    <div className="flex justify-center">
                                                        <div
                                                            className="cursor-pointer"
                                                            onClick={() => {
                                                                setPreviewImage(
                                                                    imageState.file
                                                                        ?.url || "",
                                                                );
                                                                setPreviewOpen(true);
                                                            }}
                                                        >
                                                            <div className="w-[300px] h-[300px]">
                                                                <img
                                                                    src={
                                                                        imageState.file
                                                                            .url
                                                                    }
                                                                    alt="Vista previa"
                                                                    className="w-full h-full object-contain"
                                                                />
                                                            </div>
                                                            <p className="text-center text-xs text-gray-500">
                                                                {imageState.width ||
                                                                    "-"}{" "}
                                                                ×{" "}
                                                                {imageState.height ||
                                                                    "-"}{" "}
                                                                px • Haga clic para
                                                                ampliar
                                                            </p>
                                                        </div>
                                                    </div>
                                                </div>
                                            ) : (
                                                // Placeholder
                                                <div className="grid place-content-center h-full bg-gray-50">
                                                    <ImageIcon
                                                        size={64}
                                                        strokeWidth={1}
                                                    />
                                                </div>
                                            )}
                                        </div>
                                    </div>

                                    <Image
                                        wrapperStyle={{ display: "none" }}
                                        preview={{
                                            visible: previewOpen,
                                            onVisibleChange: setPreviewOpen,
                                            toolbarRender: () => (
                                                <div className="flex items-center gap-2 text-sm">
                                                    <span>
                                                        Dimensiones: {imageState.width}{" "}
                                                        × {imageState.height} px
                                                    </span>
                                                    <span>•</span>
                                                    <span>
                                                        Descripción:{" "}
                                                        {imageState.description ||
                                                            "Sin descripción"}
                                                    </span>
                                                </div>
                                            ),
                                        }}
                                        alt={`Vista previa de ${imageState.description || "imagen"}`}
                                        src={previewImage}
                                    />
                                </div>
                            ),
                        },
                        ...(imageState.type === "thumbnail"
                            ? [
                                  {
                                      key: "preview",
                                      label: "Vista previa",
                                      children: (
                                          <BlogCardPreview
                                              blog={blog}
                                              thumbnail={thumbnail || undefined}
                                              coverImage={coverImage || undefined}
                                          />
                                      ),
                                  },
                              ]
                            : []),
                    ]}
                />
            </Modal>
        </>
    );
};

export default UploadBlogImages;
