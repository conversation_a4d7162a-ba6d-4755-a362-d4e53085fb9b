# ResponsivePreview Component

Un componente reutilizable para previsualizar contenido en diferentes tamaños de pantalla (Desktop, Tablet, Mobile) con un toolbar interactivo similar a Flowbite.

## Características

- 🖥️ **Múltiples viewports**: Desktop (1200px), Tablet (768px), Mobile (375px)
- 🎛️ **Toolbar interactivo**: Botones para cambiar entre viewports
- 📏 **Indicador de dimensiones**: Muestra las dimensiones actuales
- 🎨 **Personalizable**: Clases CSS y estilos personalizables
- ⚡ **Transiciones suaves**: Animaciones fluidas entre cambios
- 🔄 **Reutilizable**: Puede envolver cualquier contenido

## Uso Básico

```tsx
import ResponsivePreview from "@components/shared/molecules/ResponsivePreview";

function MyComponent() {
    return (
        <ResponsivePreview>
            <div>Tu contenido aquí</div>
        </ResponsivePreview>
    );
}
```

## Props

| Prop | Tipo | Default | Descripción |
|------|------|---------|-------------|
| `children` | `React.ReactNode` | - | Contenido a previsualizar |
| `defaultViewport` | `"desktop" \| "tablet" \| "mobile"` | `"desktop"` | Viewport inicial |
| `showDimensions` | `boolean` | `true` | Mostrar dimensiones en el toolbar |
| `className` | `string` | `""` | Clase CSS para el contenedor principal |
| `toolbarClassName` | `string` | `""` | Clase CSS para el toolbar |
| `previewClassName` | `string` | `""` | Clase CSS para el área de preview |

## Ejemplos de Uso

### 1. Preview Básico
```tsx
<ResponsivePreview defaultViewport="mobile">
    <MyComponent />
</ResponsivePreview>
```

### 2. Preview Personalizado
```tsx
<ResponsivePreview
    defaultViewport="tablet"
    showDimensions={false}
    className="my-custom-preview"
    toolbarClassName="bg-blue-50"
    previewClassName="p-6"
>
    <MyForm />
</ResponsivePreview>
```

### 3. Preview de Blog Card (Caso de uso actual)
```tsx
<ResponsivePreview
    defaultViewport="desktop"
    showDimensions={true}
    previewClassName="p-4"
>
    <BlogCard {...blogProps} />
</ResponsivePreview>
```

## Hook Personalizado

También puedes usar el hook `useResponsivePreview` para crear tu propia implementación:

```tsx
import { useResponsivePreview } from "@hooks/useResponsivePreview";

function CustomPreview() {
    const {
        currentViewport,
        changeViewport,
        getCurrentConfig,
        getViewportStyles,
        isViewport
    } = useResponsivePreview({
        defaultViewport: "desktop",
        onViewportChange: (viewport) => console.log(viewport)
    });

    return (
        <div>
            <div>Viewport actual: {currentViewport}</div>
            <div style={getViewportStyles()}>
                Tu contenido
            </div>
        </div>
    );
}
```

## Configuración de Viewports

Los viewports están predefinidos pero pueden ser personalizados:

```tsx
const VIEWPORT_CONFIGS = {
    desktop: { width: 1200, height: 800, name: "Escritorio" },
    tablet: { width: 768, height: 1024, name: "Tablet" },
    mobile: { width: 375, height: 667, name: "Móvil" }
};
```

## Casos de Uso

- ✅ Preview de componentes de blog
- ✅ Testing de responsive design
- ✅ Documentación de componentes
- ✅ Demos interactivos
- ✅ Validación de UI en diferentes pantallas

## Integración con Otros Componentes

El componente es completamente agnóstico y puede envolver cualquier contenido React, haciéndolo perfecto para:

- Sistemas de design
- Documentación de componentes
- Testing visual
- Demos de productos
- Validación de UX
