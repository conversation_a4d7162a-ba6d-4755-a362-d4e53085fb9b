import React from "react";
import ResponsivePreview from "./index";
import { Card, Button, Typography, Space } from "antd";

const { Title, Paragraph } = Typography;

// Ejemplo 1: Preview de una página web
export function WebPagePreview() {
    return (
        <ResponsivePreview
            defaultViewport="desktop"
            showDimensions={true}
            className="my-4"
        >
            <div className="min-h-screen bg-gray-50">
                <header className="bg-white shadow-sm p-4">
                    <div className="max-w-6xl mx-auto">
                        <Title level={2} className="mb-0">Mi Sitio Web</Title>
                    </div>
                </header>
                <main className="max-w-6xl mx-auto p-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        {[1, 2, 3, 4, 5, 6].map((item) => (
                            <Card key={item} className="h-48">
                                <Title level={4}>Tarjeta {item}</Title>
                                <Paragraph>
                                    Contenido de ejemplo para la tarjeta {item}
                                </Paragraph>
                            </Card>
                        ))}
                    </div>
                </main>
            </div>
        </ResponsivePreview>
    );
}

// Ejemplo 2: Preview de un componente específico
export function ComponentPreview({ children }: { children: React.ReactNode }) {
    return (
        <ResponsivePreview
            defaultViewport="tablet"
            showDimensions={false}
            previewClassName="p-6 bg-gray-50"
        >
            {children}
        </ResponsivePreview>
    );
}

// Ejemplo 3: Preview de un formulario
export function FormPreview() {
    return (
        <ResponsivePreview
            defaultViewport="mobile"
            showDimensions={true}
            toolbarClassName="bg-blue-50 border-blue-200"
        >
            <div className="p-6 bg-white">
                <Title level={3}>Formulario de Contacto</Title>
                <Space direction="vertical" className="w-full" size="large">
                    <div>
                        <label className="block text-sm font-medium mb-2">Nombre</label>
                        <input 
                            type="text" 
                            className="w-full p-2 border rounded-md"
                            placeholder="Tu nombre completo"
                        />
                    </div>
                    <div>
                        <label className="block text-sm font-medium mb-2">Email</label>
                        <input 
                            type="email" 
                            className="w-full p-2 border rounded-md"
                            placeholder="<EMAIL>"
                        />
                    </div>
                    <div>
                        <label className="block text-sm font-medium mb-2">Mensaje</label>
                        <textarea 
                            className="w-full p-2 border rounded-md h-24"
                            placeholder="Tu mensaje aquí..."
                        />
                    </div>
                    <Button type="primary" size="large" className="w-full">
                        Enviar Mensaje
                    </Button>
                </Space>
            </div>
        </ResponsivePreview>
    );
}

// Ejemplo 4: Preview con callback personalizado
export function CustomCallbackPreview() {
    const handleViewportChange = (viewport: string) => {
        console.log(`Viewport cambiado a: ${viewport}`);
    };

    return (
        <ResponsivePreview
            defaultViewport="desktop"
            showDimensions={true}
        >
            <div className="p-8 text-center">
                <Title level={2}>Contenido Responsive</Title>
                <Paragraph>
                    Este contenido se adapta automáticamente a diferentes tamaños de pantalla.
                    Cambia entre los diferentes viewports para ver cómo se comporta.
                </Paragraph>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mt-6">
                    {[1, 2, 3, 4].map((item) => (
                        <div key={item} className="bg-blue-100 p-4 rounded-lg">
                            <Title level={4}>Item {item}</Title>
                            <Paragraph className="text-sm">
                                Contenido del item {item}
                            </Paragraph>
                        </div>
                    ))}
                </div>
            </div>
        </ResponsivePreview>
    );
}

export default {
    WebPagePreview,
    ComponentPreview,
    FormPreview,
    CustomCallbackPreview,
};
