import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "antd";
import { MonitorIcon, TabletIcon, SmartphoneIcon } from "lucide-react";

export type ViewportSize = "desktop" | "tablet" | "mobile";

interface ViewportConfig {
    name: string;
    icon: React.ReactNode;
    width: number;
    height: number;
    className: string;
}

const VIEWPORT_CONFIGS: Record<ViewportSize, ViewportConfig> = {
    desktop: {
        name: "Escritorio",
        icon: <MonitorIcon size={16} />,
        width: 1200,
        height: 800,
        className: "w-full max-w-none",
    },
    tablet: {
        name: "Tablet",
        icon: <TabletIcon size={16} />,
        width: 768,
        height: 1024,
        className: "w-[768px]",
    },
    mobile: {
        name: "<PERSON><PERSON>vil",
        icon: <SmartphoneIcon size={16} />,
        width: 375,
        height: 667,
        className: "w-[375px]",
    },
};

interface ResponsivePreviewProps {
    children: React.ReactNode;
    defaultViewport?: ViewportSize;
    showDimensions?: boolean;
    className?: string;
    toolbarClassName?: string;
    previewClassName?: string;
}

export function ResponsivePreview({
    children,
    defaultViewport = "desktop",
    showDimensions = true,
    className = "",
    toolbarClassName = "",
    previewClassName = "",
}: ResponsivePreviewProps) {
    const [currentViewport, setCurrentViewport] =
        useState<ViewportSize>(defaultViewport);
    const config = VIEWPORT_CONFIGS[currentViewport];

    return (
        <div className={`space-y-4 ${className}`}>
            {/* Toolbar */}
            <div
                className={`flex items-center justify-between p-3 bg-gray-50 rounded-lg border ${toolbarClassName}`}
            >
                <div className="flex items-center gap-2">
                    <span className="text-sm font-medium text-gray-700">
                        Vista previa:
                    </span>
                    <div className="flex items-center gap-1 bg-white rounded-md p-1 border">
                        {(Object.keys(VIEWPORT_CONFIGS) as ViewportSize[]).map(
                            (viewport) => {
                                const viewportConfig = VIEWPORT_CONFIGS[viewport];
                                const isActive = currentViewport === viewport;

                                return (
                                    <Tooltip key={viewport} title={viewportConfig.name}>
                                        <Button
                                            type={isActive ? "primary" : "text"}
                                            size="small"
                                            icon={viewportConfig.icon}
                                            onClick={() => setCurrentViewport(viewport)}
                                            className={`flex items-center gap-1 ${
                                                isActive
                                                    ? "bg-blue-500 text-white border-blue-500"
                                                    : "text-gray-600 hover:text-blue-500"
                                            }`}
                                        >
                                            <span className="hidden sm:inline">
                                                {viewportConfig.name}
                                            </span>
                                        </Button>
                                    </Tooltip>
                                );
                            },
                        )}
                    </div>
                </div>

                {/* Dimensiones */}
                {showDimensions && (
                    <div className="text-xs text-gray-500 bg-white px-2 py-1 rounded border">
                        {config.width} × {config.height}px
                    </div>
                )}
            </div>

            {/* Preview Container */}
            <div className="flex justify-center">
                <div
                    className={`
                        transition-all duration-300 ease-in-out
                        border rounded-lg bg-white shadow-sm
                        ${config.className}
                        ${previewClassName}
                    `}
                    style={{
                        maxWidth:
                            currentViewport === "desktop"
                                ? "100%"
                                : `${config.width}px`,
                        minHeight: currentViewport === "mobile" ? "400px" : "auto",
                    }}
                >
                    {/* Content */}
                    <div className="w-full h-full overflow-auto">{children}</div>
                </div>
            </div>

            {/* Viewport Info */}
            <div className="text-center">
                <span className="text-xs text-gray-400">
                    Previsualizando en {config.name.toLowerCase()} ({config.width}×
                    {config.height}px)
                </span>
            </div>
        </div>
    );
}

export default ResponsivePreview;
